/**
 * 访问统计模块
 * 负责集成和管理不蒜子访问统计功能
 * 版本: v4.0.0
 * 变更记录:
 * - v4.0.0 (2025-01-28): 重大改进 - 移除页面显示，改为控制台输出统计数据，提升页面简洁性
 * - v3.1.0 (2025-01-28): 优化本地环境检测，增强错误处理，修复控制台404错误
 * - v3.0.0 (2025-01-27): 新增本地环境检测 - 在开发环境下自动禁用统计功能，提升开发体验
 * - v2.0.0 (2025-01-27): 重大简化 - 移除过度复杂的错误处理和重试机制，提升代码可维护性
 * - v1.1.0 (2025-07-27): 优化脚本检测逻辑，改进数据加载检测机制
 * - v1.0.0 (2025-07-27): 初始版本，集成不蒜子访问统计功能
 */

class Statistics {
    constructor() {
        this.config = window.AppConfig?.statistics || {};
        this.initialized = false;
        // 移除container相关属性，改为控制台输出
        this.statisticsData = {
            sitePV: null,
            siteUV: null,
            pagePV: null
        };
        // 使用全局环境检测模块，如果不存在则回退到本地检测
        this.isLocalEnvironment = window.AppEnvironment?.isLocal ?? this.detectLocalEnvironment();
    }

    /**
     * 检测是否为本地开发环境
     * @returns {boolean} 如果是本地环境返回true，否则返回false
     */
    detectLocalEnvironment() {
        const location = window.location;
        const hostname = location.hostname;
        const protocol = location.protocol;
        const port = location.port;

        // 检测本地主机名
        const localHostnames = ['localhost', '127.0.0.1', '0.0.0.0'];
        if (localHostnames.includes(hostname)) {
            return true;
        }

        // 检测file协议
        if (protocol === 'file:') {
            return true;
        }

        // 检测常见开发端口
        const developmentPorts = ['3000', '8000', '8080', '5000', '4000', '9000', '3001', '8001'];
        if (port && developmentPorts.includes(port)) {
            return true;
        }

        // 检测本地IP地址范围
        if (hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
            return true;
        }

        return false;
    }

    /**
     * 初始化统计功能
     */
    init() {
        if (this.initialized) return;

        // 在本地环境下禁用统计功能
        if (this.isLocalEnvironment) {
            this.logLocalEnvironmentMessage();
            this.initialized = true;
            return;
        }

        this.loadBusuanziScript();
        this.createHiddenElements();
        this.waitForData();
        this.initialized = true;
    }

    /**
     * 动态加载不蒜子统计脚本
     */
    loadBusuanziScript() {
        // 再次检查是否为本地环境，防止意外加载
        if (this.isLocalEnvironment) {
            console.info('[Statistics] 本地环境下跳过脚本加载');
            return;
        }

        // 检查脚本是否已经加载
        if (document.querySelector('script[src*="busuanzi"]')) {
            return;
        }

        const script = document.createElement('script');
        script.async = true;
        script.src = '//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js';
        script.onerror = () => {
            console.warn('[Statistics] 不蒜子脚本加载失败，统计功能将不可用');
        };
        script.onload = () => {
            console.info('[Statistics] 不蒜子脚本加载成功');
        };
        document.head.appendChild(script);
    }

    /**
     * 在控制台输出本地环境提示信息
     */
    logLocalEnvironmentMessage() {
        const location = window.location;
        console.info(
            '%c[Statistics] 统计功能已在开发环境下禁用',
            'color: #4F86C6; font-weight: bold;',
            `\n当前环境: ${location.protocol}//${location.hostname}${location.port ? ':' + location.port : ''}`,
            '\n统计功能将在生产环境中正常工作'
        );
    }

    /**
     * 创建隐藏的统计元素（用于不蒜子数据收集，但不显示在页面上）
     */
    createHiddenElements() {
        // 创建隐藏的容器，仅用于不蒜子脚本识别和数据收集
        const hiddenContainer = document.createElement('div');
        hiddenContainer.style.display = 'none'; // 完全隐藏
        hiddenContainer.innerHTML = this.buildHiddenStatisticsHTML();

        // 添加到body末尾
        document.body.appendChild(hiddenContainer);

        console.info(
            '%c[Statistics] 统计元素已创建（隐藏模式）',
            'color: #4F86C6; font-weight: bold;',
            '\n统计数据将在控制台显示，不影响页面布局'
        );
    }

    /**
     * 构建隐藏的统计HTML（仅用于不蒜子脚本识别）
     */
    buildHiddenStatisticsHTML() {
        return `
            <span id="busuanzi_container_site_pv">
                <span id="busuanzi_value_site_pv">--</span>
            </span>
            <span id="busuanzi_container_site_uv">
                <span id="busuanzi_value_site_uv">--</span>
            </span>
            <span id="busuanzi_container_page_pv">
                <span id="busuanzi_value_page_pv">--</span>
            </span>
        `;
    }

    /**
     * 等待数据加载并输出到控制台
     */
    waitForData() {
        const checkData = () => {
            const sitePV = document.getElementById('busuanzi_value_site_pv');
            const siteUV = document.getElementById('busuanzi_value_site_uv');
            const pagePV = document.getElementById('busuanzi_value_page_pv');

            // 检查是否有数据更新
            const hasData = (sitePV && sitePV.textContent !== '--') ||
                           (siteUV && siteUV.textContent !== '--') ||
                           (pagePV && pagePV.textContent !== '--');

            if (hasData) {
                this.collectAndDisplayStatistics();
            } else {
                setTimeout(checkData, 1000);
            }
        };

        setTimeout(checkData, 2000); // 延迟2秒开始检查
    }

    /**
     * 收集并在控制台显示统计信息
     */
    collectAndDisplayStatistics() {
        // 收集统计数据
        const sitePV = document.getElementById('busuanzi_value_site_pv');
        const siteUV = document.getElementById('busuanzi_value_site_uv');
        const pagePV = document.getElementById('busuanzi_value_page_pv');

        // 更新数据对象
        this.statisticsData = {
            sitePV: sitePV ? sitePV.textContent : null,
            siteUV: siteUV ? siteUV.textContent : null,
            pagePV: pagePV ? pagePV.textContent : null
        };

        // 在控制台显示统计信息
        console.info(
            '%c[Statistics] 网站访问统计',
            'color: #4F86C6; font-weight: bold;',
            '\n网站总访问量:', this.statisticsData.sitePV || '数据加载中...',
            '\n独立访客数:', this.statisticsData.siteUV || '数据加载中...',
            '\n当前页面访问量:', this.statisticsData.pagePV || '数据加载中...'
        );

        // 设置定期更新（每60秒刷新一次数据）
        setTimeout(() => this.refreshStatistics(), 60000);
    }

    /**
     * 刷新统计数据（定期更新）
     */
    refreshStatistics() {
        const sitePV = document.getElementById('busuanzi_value_site_pv');
        const siteUV = document.getElementById('busuanzi_value_site_uv');
        const pagePV = document.getElementById('busuanzi_value_page_pv');

        // 检查数据是否有变化
        const newData = {
            sitePV: sitePV ? sitePV.textContent : null,
            siteUV: siteUV ? siteUV.textContent : null,
            pagePV: pagePV ? pagePV.textContent : null
        };

        // 只有数据发生变化时才更新显示
        if (JSON.stringify(newData) !== JSON.stringify(this.statisticsData)) {
            this.statisticsData = newData;
            console.info(
                '%c[Statistics] 统计数据已更新',
                'color: #10B981; font-weight: bold;',
                '\n网站总访问量:', this.statisticsData.sitePV || '数据加载中...',
                '\n独立访客数:', this.statisticsData.siteUV || '数据加载中...',
                '\n当前页面访问量:', this.statisticsData.pagePV || '数据加载中...'
            );
        }

        // 继续定期刷新
        setTimeout(() => this.refreshStatistics(), 60000);
    }

    /**
     * 检查统计功能是否可用
     * @returns {boolean} 如果统计功能可用返回true，否则返回false
     */
    isEnabled() {
        return !this.isLocalEnvironment;
    }

    /**
     * 获取当前统计数据
     * @returns {Object} 包含统计数据的对象
     */
    getStatisticsData() {
        return {
            ...this.statisticsData,
            isEnabled: this.isEnabled(),
            isLocal: this.isLocalEnvironment,
            lastUpdated: new Date().toISOString()
        };
    }

    /**
     * 获取当前环境信息
     * @returns {Object} 包含环境信息的对象
     */
    getEnvironmentInfo() {
        const location = window.location;
        return {
            isLocal: this.isLocalEnvironment,
            hostname: location.hostname,
            protocol: location.protocol,
            port: location.port,
            url: location.href
        };
    }

    /**
     * 销毁统计功能
     */
    destroy() {
        // 清理隐藏的统计元素
        const hiddenElements = document.querySelectorAll('#busuanzi_container_site_pv, #busuanzi_container_site_uv, #busuanzi_container_page_pv');
        hiddenElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        this.initialized = false;
        this.statisticsData = {
            sitePV: null,
            siteUV: null,
            pagePV: null
        };

        console.info(
            '%c[Statistics] 统计功能已销毁',
            'color: #EF4444; font-weight: bold;'
        );
    }
}

// 导出统计模块
window.Statistics = Statistics;
